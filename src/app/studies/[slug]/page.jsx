'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import SearchBar from '@/components/SearchBar';
import ProfileDropdown from '@/components/ProfileDropdown';
import NotificationCenter from '@/components/NotificationCenter';
import HeaderTestButton from '@/components/HeaderTestButton';
import AuthTestButton from '@/components/AuthTestButton';
import ResponsiveTestPanel from '@/components/ResponsiveTestPanel';
import AnimeMenuButton from '@/components/AnimeMenuButton';
import AnimeDropdownMenu from '@/components/AnimeDropdownMenu';
import AnimeSearchBar from '@/components/AnimeSearchBar';
import { useAuth } from '@/contexts/AuthContext';
import styles from './StudioPage.module.css';
import Link from "next/link";

export default function StudioPage() {
    const { user, isAuthenticated, logout } = useAuth();
    const [showTestComponents, setShowTestComponents] = useState(false);
    const [studio, setStudio] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const params = useParams();
    const router = useRouter();

    const toggleTestComponents = () => {
        setShowTestComponents(!showTestComponents);
    };

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const closeMenu = () => {
        setIsMenuOpen(false);
    };

    useEffect(() => {
        fetchStudio();
    }, [params.slug]);

    const fetchStudio = async () => {
        try {
            setLoading(true);
            const response = await fetch('http://localhost:3002/studies');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // Знаходимо студію за slug
            const foundStudio = data.find(studio => {
                const slug = studio.name.toLowerCase()
                    .replace(/\s+/g, '-')
                    .replace(/[^\w\-]+/g, '')
                    .replace(/\-\-+/g, '-')
                    .replace(/^-+/, '')
                    .replace(/-+$/, '');
                return slug === params.slug;
            });

            if (!foundStudio) {
                throw new Error('Студію не знайдено');
            }

            setStudio(foundStudio);
        } catch (err) {
            console.error('Error fetching studio:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const handleBackClick = () => {
        router.push('/studies');
    };

    if (loading) {
        return (
            <div className={styles.container}>
                <div className={styles.loading}>
                    <div className={styles.spinner}></div>
                    <p>Завантаження студії...</p>
                </div>
            </div>
        );
    }

    if (error || !studio) {
        return (
            <div className={styles.container}>
                <div className={styles.error}>
                    <p>Помилка: {error || 'Студію не знайдено'}</p>
                    <button onClick={handleBackClick} className={styles.backButton}>
                        Повернутися до списку студій
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <img src="/Line 5.svg" className={styles.line5} />
            <img src="/Line 10.svg" className={styles.line10} />
            <img src="/Line 9.svg" className={styles.line9} />
            
            <header className={styles.header}>
                <div className={styles.header_left}>
                    <AnimeMenuButton onClick={toggleMenu} isActive={isMenuOpen} />
                </div>
                <img src="/Group 1.svg" className={styles.logo} />
                <div style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    width: '100%',
                    gap: 'clamp(12px, 2vw, 24px)',
                    flexWrap: 'wrap'
                }}>
                    <div className={styles.searchBar}>
                        <SearchBar />
                    </div>
                    <div className={styles.notificationCenter}>
                        <NotificationCenter />
                    </div>
                    <HeaderTestButton
                        showTestComponents={showTestComponents}
                        toggleTestComponents={toggleTestComponents}
                    />
                    {isAuthenticated ? (
                        <div className={styles.headerProfileDropdown}>
                            <ProfileDropdown user={user} logout={logout} />
                        </div>
                    ) : (
                        <div className={styles.header_buttons}>
                            <Link href="/signin">
                                <button className="header_button_login">
                                    Увійти
                                </button>
                            </Link>
                            <Link href="/signup">
                                <button className="header_button_signup">
                                    Реєстрація
                                </button>
                            </Link>
                        </div>
                    )}
                </div>
            </header>
            <AnimeDropdownMenu isOpen={isMenuOpen} onClose={closeMenu} />

            <div className={styles.studioSection}>
                <div className={styles.breadcrumb}>
                    <button onClick={handleBackClick} className={styles.breadcrumbLink}>
                        Студії
                    </button>
                    <span className={styles.breadcrumbSeparator}>/</span>
                    <span className={styles.breadcrumbCurrent}>{studio.name}</span>
                </div>
            </div>

            <div className={styles.studioContent}>
                <div className={styles.studioInfo}>
                    <div className={styles.studioImageContainer}>
                        <img
                            src={studio.logo}
                            alt={studio.name}
                            className={styles.studioImage}
                            onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'flex';
                            }}
                        />
                        <div className={styles.placeholderImage} style={{ display: 'none' }}>
                            <span className={styles.placeholderText}>
                                {studio.name?.charAt(0) || 'S'}
                            </span>
                        </div>
                    </div>

                    <div className={styles.studioDetails}>
                        <div className={styles.studioDescription}>
                            <h1 className={styles.descriptionTitle}>{studio.name}</h1>
                            <div
                                className={styles.description}
                                dangerouslySetInnerHTML={{ __html: studio.description_for_page }}
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Пошук аніме */}
            <div className={styles.animeSearchSection}>
                <AnimeSearchBar placeholder="Введіть назву аніме..." />
            </div>

            <img src="/Line 6.svg" className={styles.line6} />
            <img src="/Line 8.svg" className={styles.line8} />
            <img src="/Line 7.svg" className={styles.line7} />

            {/* Тестові компоненти з'являються тільки при активації */}
            {showTestComponents && (
                <>
                    <AuthTestButton />
                    <ResponsiveTestPanel />
                </>
            )}
        </div>
    );
}
