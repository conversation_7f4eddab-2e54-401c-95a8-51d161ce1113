'use client';

import { useState } from 'react';
import Link from 'next/link';
import ProfileDropdown from '@/components/ProfileDropdown';
import AnimeMenuButton from '@/components/AnimeMenuButton';
import AnimeDropdownMenu from '@/components/AnimeDropdownMenu';

export default function Page() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <div className="container">
      <header className="header_main_page">
          <div className="header_left">
              <AnimeMenuButton onClick={toggleMenu} isActive={isMenuOpen} />
          </div>
          <div className="header_buttons">
              <ProfileDropdown />
              <Link href="/signin">
                <button
                    className="header_button_login"
                >
                    Увійти
                </button>
              </Link>
              <Link href="/signup">
                <button
                    className="header_button_signup"
                >
                    Реєстрація
                </button>
              </Link>
          </div>
      </header>
      <AnimeDropdownMenu isOpen={isMenuOpen} onClose={closeMenu} />
      <main>

      </main>
      <footer></footer>
    </div>
  );
}
