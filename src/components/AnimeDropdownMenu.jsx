'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from './AnimeDropdownMenu.module.css';

const AnimeDropdownMenu = ({ isOpen, onClose }) => {
    const menuRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return (
        <div className={styles.overlay}>
            <div ref={menuRef} className={styles.menu}>
                <div className={styles.section}>
                    <h3 className={styles.sectionTitle}>Контент</h3>
                    <ul className={styles.menuList}>
                        <li className={styles.menuItem}>
                            <Link href="/anime" className={styles.menuLink}>
                                <span className={styles.icon}>📺</span>
                                Аніме
                            </Link>
                        </li>
                        <li className={styles.menuItem}>
                            <Link href="/manga" className={styles.menuLink}>
                                <span className={styles.icon}>😊</span>
                                Манга
                            </Link>
                        </li>
                        <li className={styles.menuItem}>
                            <Link href="/ranobe" className={styles.menuLink}>
                                <span className={styles.icon}>📚</span>
                                Ранобе
                            </Link>
                        </li>
                    </ul>
                </div>

                <div className={styles.section}>
                    <h3 className={styles.sectionTitle}>Спільнота</h3>
                    <ul className={styles.menuList}>
                        <li className={styles.menuItem}>
                            <Link href="/articles" className={styles.menuLink}>
                                <span className={styles.icon}>📰</span>
                                Статті
                            </Link>
                            <ul className={styles.submenu}>
                                <li className={styles.submenuItem}>
                                    <Link href="/articles/news" className={styles.submenuLink}>
                                        <span className={styles.submenuIcon}>📄</span>
                                        Новини
                                    </Link>
                                </li>
                                <li className={styles.submenuItem}>
                                    <Link href="/articles/reviews" className={styles.submenuLink}>
                                        <span className={styles.submenuIcon}>📹</span>
                                        Огляди
                                    </Link>
                                </li>
                                <li className={styles.submenuItem}>
                                    <Link href="/articles/author" className={styles.submenuLink}>
                                        <span className={styles.submenuIcon}>⭐</span>
                                        Авторське
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className={styles.menuItem}>
                            <Link href="/collections" className={styles.menuLink}>
                                <span className={styles.icon}>📚</span>
                                Колекції
                            </Link>
                        </li>
                        <li className={styles.menuItem}>
                            <Link href="/edits" className={styles.menuLink}>
                                <span className={styles.icon}>✏️</span>
                                Правки
                            </Link>
                        </li>
                    </ul>
                </div>

                <div className={styles.section}>
                    <h3 className={styles.sectionTitle}>Інше</h3>
                    <ul className={styles.menuList}>
                        <li className={styles.menuItem}>
                            <Link href="/calendar" className={styles.menuLink}>
                                <span className={styles.icon}>📅</span>
                                Календар
                            </Link>
                        </li>
                    </ul>
                </div>

                <div className={styles.section}>
                    <h3 className={styles.sectionTitle}>Соцмережі</h3>
                    <ul className={styles.menuList}>
                        <li className={styles.menuItem}>
                            <a href="https://t.me/your_channel" target="_blank" rel="noopener noreferrer" className={styles.menuLink}>
                                <span className={styles.icon}>✈️</span>
                                Telegram
                            </a>
                        </li>
                        <li className={styles.menuItem}>
                            <a href="https://donatello.to/your_account" target="_blank" rel="noopener noreferrer" className={styles.menuLink}>
                                <span className={styles.icon}>💰</span>
                                Donatello
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default AnimeDropdownMenu;
