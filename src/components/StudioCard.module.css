.studioCard {
    display: flex;
    gap: 16px;
    cursor: pointer;
    width: 828px;
    height: 500px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.studioCard:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.imageContainer {
    flex-shrink: 0;
    width: 300px;
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
    overflow: hidden;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.studioImage {
    width: 100%;
    object-fit: cover;
}

.placeholderImage {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #49638A, #5a7bb5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
}

.placeholderText {
    font-size: 120px;
    font-weight: bold;
    color: white;
    text-transform: uppercase;
}

.studioInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 500px;
    min-width: 0;
    position: relative;
    padding-bottom: 40px;
}

.studioHeader {
    margin-bottom: 0;
}

.studioName {
    font-family: 'Inter', sans-serif;
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    margin: 0 0 0 0;
    line-height: 1.2;
}

.studioDescription {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 480.1715px;
    height: 232px;
    margin: 0 0;
}

.subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    color: #4B7FCC;
    margin: 0 0 0 0;
    line-height: normal;
}

.description {
    font-family: 'Inter', sans-serif;
    width: 100%;
    height: 261px;
    font-size: 24px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin: 0;
}

/* Стилі для HTML елементів в описі */
.description span {
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    color: #4B7FCC;
    margin: 0 0 0 0;
    line-height: normal;
}

/*.description br {*/
/*    display: block;*/
/*    margin: 4px 0;*/
/*    content: "";*/
/*}*/

.releasesCount {
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.count {
    color: #787880;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}


/* Адаптивність */
@media (max-width: 768px) {
    .studioCard {
        flex-direction: column;
        padding: 16px;
        gap: 16px;
        max-width: 100%;
    }
    
    .imageContainer {
        width: 100px;
        height: 100px;
        align-self: center;
    }
    
    .studioName {
        font-size: 20px;
        text-align: center;
    }
    
    .releasesCount {
        align-items: center;
        flex-direction: row;
        justify-content: center;
        gap: 8px;
    }
    
    .description {
        text-align: center;
    }
    
    .subtitle {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .studioCard {
        padding: 12px;
        gap: 12px;
    }
    
    .imageContainer {
        width: 80px;
        height: 80px;
    }
    
    .studioName {
        font-size: 18px;
    }
    
    .description {
        font-size: 13px;
    }
    
    .placeholderText {
        font-size: 32px;
    }
}
