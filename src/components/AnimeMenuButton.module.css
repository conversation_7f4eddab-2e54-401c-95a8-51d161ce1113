.animeButton {
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    border: 1px solid #49638A;
    border-radius: 18px;
    color: #fff;
    font-family: 'Inter', sans-serif;
    font-size: clamp(14px, 1.5vw, 16px);
    font-weight: 500;
    padding: clamp(12px, 1.5vh, 15.5px) clamp(20px, 3vw, 31px);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    z-index: 1001;
}

.animeButton:hover {
    background: rgba(73, 99, 138, 0.2);
    transform: translateY(-1px);
}

.animeButton.active {
    background: rgba(73, 99, 138, 0.3);
    border-color: #5a7bb5;
}

.buttonText {
    font-size: inherit;
    font-weight: inherit;
}

.arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
    color: #fff;
}

.arrow.arrowUp {
    transform: rotate(180deg);
}
