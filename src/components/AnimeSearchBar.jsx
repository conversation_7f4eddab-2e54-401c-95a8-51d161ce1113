'use client';

import { useState } from 'react';
import styles from './AnimeSearchBar.module.css';

const AnimeSearchBar = ({ placeholder = "Введіть назву аніме..." }) => {
    const [searchValue, setSearchValue] = useState('');

    const handleInputChange = (e) => {
        setSearchValue(e.target.value);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (searchValue.trim()) {
            // Тут буде логіка пошуку аніме
            console.log('Searching for:', searchValue);
        }
    };

    return (
        <div className={styles.searchContainer}>
            <form onSubmit={handleSubmit} className={styles.searchForm}>
                <div className={styles.searchInputContainer}>
                    <input
                        type="text"
                        value={searchValue}
                        onChange={handleInputChange}
                        placeholder={placeholder}
                        className={styles.searchInput}
                    />
                    <button type="submit" className={styles.searchButton}>
                        <svg 
                            width="20" 
                            height="20" 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            xmlns="http://www.w3.org/2000/svg"
                            className={styles.searchIcon}
                        >
                            <path 
                                d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" 
                                stroke="currentColor" 
                                strokeWidth="2" 
                                strokeLinecap="round" 
                                strokeLinejoin="round"
                            />
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    );
};

export default AnimeSearchBar;
