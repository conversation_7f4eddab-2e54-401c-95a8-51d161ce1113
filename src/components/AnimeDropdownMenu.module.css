.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.menu {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    margin-top: 100px;
    margin-left: 20px;
    width: 280px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    color: #fff;
    font-family: 'Inter', sans-serif;
}

.section {
    margin-bottom: 24px;
}

.section:last-child {
    margin-bottom: 0;
}

.sectionTitle {
    color: #888;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 0 12px 0;
    padding-left: 8px;
}

.menuList {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menuItem {
    margin-bottom: 4px;
}

.menuLink {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 8px;
    color: #fff;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 400;
}

.menuLink:hover {
    background: rgba(73, 99, 138, 0.2);
    color: #fff;
}

.icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 8px 0 0 32px;
    border-left: 1px solid #333;
}

.submenuItem {
    margin-bottom: 2px;
}

.submenuLink {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    color: #ccc;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 13px;
    font-weight: 400;
}

.submenuLink:hover {
    background: rgba(73, 99, 138, 0.15);
    color: #fff;
}

.submenuIcon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

/* Scrollbar styling */
.menu::-webkit-scrollbar {
    width: 6px;
}

.menu::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 3px;
}

.menu::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
}

.menu::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .menu {
        width: calc(100vw - 40px);
        margin-left: 20px;
        margin-right: 20px;
    }
}
