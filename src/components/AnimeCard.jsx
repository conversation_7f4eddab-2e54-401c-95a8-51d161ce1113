'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './AnimeCard.module.css';

const AnimeCard = ({ anime }) => {
    const [imageError, setImageError] = useState(false);
    const router = useRouter();

    const handleImageError = () => {
        setImageError(true);
    };

    const handleCardClick = () => {
        // Тут буде логіка переходу на сторінку аніме
        console.log('Clicked on anime:', anime.name);
    };

    const formatRating = (rating) => {
        return rating ? rating.toFixed(2) : 'N/A';
    };

    const formatEpisodes = (episodes) => {
        if (!episodes) return '';
        return episodes === 1 ? '1 епізод' : `${episodes} епізодів`;
    };

    return (
        <div className={styles.animeCard} onClick={handleCardClick}>
            <div className={styles.imageContainer}>
                {!imageError ? (
                    <img
                        src={anime.image}
                        alt={anime.name}
                        className={styles.animeImage}
                        onError={handleImageError}
                    />
                ) : (
                    <div className={styles.placeholderImage}>
                        <span className={styles.placeholderText}>
                            {anime.name?.charAt(0) || 'A'}
                        </span>
                    </div>
                )}
                
                <div className={styles.overlay}>
                    <div className={styles.rating}>
                        ⭐ {formatRating(anime.rating)}
                    </div>
                </div>
            </div>

            <div className={styles.animeInfo}>
                <h4 className={styles.animeName} title={anime.name}>
                    {anime.name}
                </h4>
                
                <div className={styles.animeDetails}>
                    <span className={styles.year}>{anime.year}</span>
                    <span className={styles.type}>{anime.type}</span>
                </div>
                
                {anime.episodes && (
                    <div className={styles.episodes}>
                        {formatEpisodes(anime.episodes)}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AnimeCard;
