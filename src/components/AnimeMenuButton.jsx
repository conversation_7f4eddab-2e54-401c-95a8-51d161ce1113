'use client';

import { useState } from 'react';
import styles from './AnimeMenuButton.module.css';

const AnimeMenuButton = ({ onClick, isActive }) => {
    return (
        <button
            className={`${styles.animeButton} ${isActive ? styles.active : ''}`}
            onClick={onClick}
        >
            <span className={styles.buttonText}>Аніме</span>
            <span className={`${styles.arrow} ${isActive ? styles.arrowUp : ''}`}>▼</span>
        </button>
    );
};

export default AnimeMenuButton;
