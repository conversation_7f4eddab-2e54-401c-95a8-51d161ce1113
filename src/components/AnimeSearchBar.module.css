.searchContainer {
    width: 100%;
    margin: 48px 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.searchInputContainer {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid #333;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.searchInputContainer:focus-within {
    border-color: #49638A;
    box-shadow: 0 0 0 2px rgba(73, 99, 138, 0.2);
}

.searchIconContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 16px;
    color: rgba(255, 255, 255, 0.6);
    pointer-events: none;
}

.searchIcon {
    width: 20px;
    height: 20px;
    color: inherit;
    transition: color 0.3s ease;
}

.searchInputContainer:focus-within .searchIcon {
    color: #49638A;
}

.searchInput {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    padding: 16px 16px 16px 0;
    color: #fff;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 400;
    placeholder-color: rgba(255, 255, 255, 0.5);
}

.searchInput::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}

.loadingContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 20px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(73, 99, 138, 0.3);
    border-top: 2px solid #49638A;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Адаптивність */
@media (max-width: 768px) {
    .searchContainer {
        margin: 24px 0;
    }

    .searchIconContainer {
        padding: 14px 16px;
    }

    .searchInput {
        padding: 14px 16px 14px 0;
        font-size: 15px;
    }

    .loadingContainer {
        padding: 14px 16px;
    }

    .searchIcon,
    .spinner {
        width: 18px;
        height: 18px;
    }
}

@media (max-width: 480px) {
    .searchContainer {
        margin: 20px 0;
    }

    .searchIconContainer {
        padding: 12px 14px;
    }

    .searchInput {
        padding: 12px 14px 12px 0;
        font-size: 14px;
    }

    .loadingContainer {
        padding: 12px 14px;
    }

    .searchIcon,
    .spinner {
        width: 16px;
        height: 16px;
    }
}
